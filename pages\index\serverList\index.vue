<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<view class="server-list-item mt-16 p-16">
				<view class="flex items-center justify-between">
					<view class="flex items-center justify-between">
						<image src="@/static/server/server-logo.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col justify-between">
							<view class="flex items-center">
								<text class="text-30 font-bold">服务器列表</text>
								<text class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24">在线2天</text>
							</view>
							<text class="text-24 mt-10 text-#999">IP：服务器列表</text>
						</view>
					</view>
					<view class="flex items-center justify-between rd-16 px-8 py-4" style="border: 3rpx solid rgba(0, 0, 0, 0.1);">
						<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-64 h-64 mr-20" />
						<view class="flex flex-col items-center justify-between">
							<text class="text-20">CPU</text>
							<text class="text-20">100%</text>
						</view>
					</view>
				</view>
				<!-- 服务器监控指标网格 -->
				<view class="metrics-grid">
					<!-- 负载指标 - 第1列，跨2行 -->
					<view class="load-card">
						<text class="load-title">负载</text>
						<text class="load-status">流畅</text>
						<view class="load-chart-container">
							<view class="load-chart-fill">
								<text class="load-percentage">70%</text>
							</view>
						</view>
					</view>

					<!-- CPU指标 - 第2列第1行 -->
					<view class="metric-item cpu-item">
						<text class="metric-title">CPU</text>
						<text class="metric-subtitle">4核</text>
						<view class="arc-container">
							<view class="arc-progress cpu-arc"></view>
							<text class="arc-percentage">40%</text>
						</view>
					</view>

					<!-- 内存指标 - 第3列第1行 -->
					<view class="metric-item memory-item">
						<text class="metric-title">内存</text>
						<text class="metric-subtitle">8GB</text>
						<view class="arc-container">
							<view class="arc-progress memory-arc"></view>
							<text class="arc-percentage">60%</text>
						</view>
					</view>

					<!-- 磁盘指标 - 第2-3列第2行 -->
					<view class="disk-item">
						<view class="disk-header">
							<text class="disk-title">磁盘(/)</text>
							<text class="disk-total">40GB</text>
						</view>
						<view class="disk-progress-container">
							<view class="disk-progress-bar">
								<view class="disk-progress-fill"></view>
							</view>
							<text class="disk-percentage">48%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import { pageContainer } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
	}

	/* 监控指标网格布局 */
	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: repeat(2, 1fr);
		gap: 16rpx;
		margin-top: 32rpx;
		height: 300rpx; /* 设置固定高度确保2行布局 */
	}

	/* 负载卡片 - 第1列，跨2行 */
	.load-card {
		grid-column: 1;
		grid-row: 1 / 3;
		background: rgba(247, 247, 247, 1);
		border-radius: 24rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.load-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		align-self: flex-start;
	}

	.load-status {
		font-size: 24rpx;
		color: #20a50a;
		margin-bottom: 24rpx;
		align-self: flex-start;
	}

	/* 柱形图容器 */
	.load-chart-container {
		flex: 1;
		width: 80rpx;
		background: #f0f0f0;
		border-radius: 20rpx;
		position: relative;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		overflow: hidden;
	}

	/* 柱形图填充 */
	.load-chart-fill {
		width: 100%;
		height: 70%;
		background: linear-gradient(180deg, #f9d71c 0%, #20a50a 100%);
		border-radius: 0 0 20rpx 20rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.load-percentage {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
	}

	.metric-item {
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	/* CPU指标 - 第2列第1行 */
	.cpu-item {
		grid-column: 2;
		grid-row: 1;
	}

	/* 内存指标 - 第3列第1行 */
	.memory-item {
		grid-column: 3;
		grid-row: 1;
	}

	.metric-title {
		font-size: 24rpx;
		color: #333;
		margin-bottom: 4rpx;
	}

	.metric-subtitle {
		font-size: 20rpx;
		color: #20a50a;
		margin-bottom: 12rpx;
	}

	/* 弧形进度条容器 */
	.arc-container {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.arc-progress {
		width: 60rpx;
		height: 30rpx;
		border-radius: 60rpx 60rpx 0 0;
		position: relative;
		overflow: hidden;
		background: #f0f0f0;

		&::before {
			content: '';
			position: absolute;
			width: 100%;
			height: 100%;
			border-radius: 60rpx 60rpx 0 0;
			background: #20a50a;
		}

		&.cpu-arc::before {
			transform: rotate(-108deg);
			transform-origin: center bottom;
		}

		&.memory-arc::before {
			transform: rotate(-72deg);
			transform-origin: center bottom;
		}
	}

	.arc-percentage {
		position: absolute;
		font-size: 20rpx;
		font-weight: bold;
		color: #20a50a;
		top: 32rpx;
	}

	/* 磁盘指标 - 第2-3列第2行 */
	.disk-item {
		grid-column: 2 / 4;
		grid-row: 2;
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 24rpx;
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.disk-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.disk-title {
		font-size: 24rpx;
		color: #333;
	}

	.disk-total {
		font-size: 20rpx;
		color: #20a50a;
	}

	.disk-progress-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.disk-progress-bar {
		flex: 1;
		height: 16rpx;
		background: #f0f0f0;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
	}

	.disk-progress-fill {
		width: 48%;
		height: 100%;
		background: linear-gradient(90deg, #20a50a 0%, #4cd964 100%);
		border-radius: 8rpx;
		transition: width 0.3s ease;
	}

	.disk-percentage {
		font-size: 24rpx;
		font-weight: bold;
		color: #20a50a;
		min-width: 60rpx;
		text-align: right;
	}
</style>
